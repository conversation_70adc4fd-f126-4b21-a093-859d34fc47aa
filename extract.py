import pandas as pd

# Charger les CSV
df_phase = pd.read_csv('C:\Users\<USER>\Documents\extract\bouffon.csv', delimiter=',')
df_projet = pd.read_csv('C:\Users\<USER>\Documents\extract\extract_projet_test.csv', delimiter=',')

# Convertir les noms de colonnes en minuscules
df_phase.columns = df_phase.columns.str.lower()        # Colonnes : id, level, description, project_code, status, workcenter
df_projet.columns = df_projet.columns.str.lower()        # Colonnes : id, otp, title, project_manager_id

# Nettoyer la colonne project_code : supprimer le tiret après le "P" s'il existe (ex: 'P-20006-01-PE' -> 'P20006-01-PE')
df_phase['project_code'] = df_phase['project_code'].astype(str).str.replace(r'^P-', 'P', regex=True)

# Initialiser une liste pour stocker les résultats de la jointure
result = []

# Parcourir chaque projet et rechercher les phases dont project_code commence par l'otp du projet
for _, proj in df_projet.iterrows():
    otp = str(proj['otp'])
    # Filtrer les phases qui commencent par l'otp
    matched_phases = df_phase[df_phase['project_code'].astype(str).str.startswith(otp)]
    
    # Pour chaque phase correspondante, combiner les infos du projet et de la phase
    for _, phase in matched_phases.iterrows():
        # if phase['description'] == 'Cross operations' pass
        if phase['description'] != 'Cross operations':
            combined = {
                'phase_id': phase['id'],                # On utilisera ce champ comme "id" dans la table phase
                'level': phase['level'],
                'title': phase['description'],          # On prend ici "description" pour le titre
                'code': phase['project_code'],
                'status': 1,
                'projet_id': proj['id'],
                'status_txt': phase['status'], 
                'status_manuel': 1,
                'commentaire': 'old phase test'
            }
            result.append(combined)

# Convertir la liste de résultats en DataFrame
df_joined = pd.DataFrame(result)

# Générer le fichier SQL avec les requêtes INSERT
with open('insert_phase.sql', 'w', encoding='utf-8') as f:
    for _, row in df_joined.iterrows():
        # Convertir en chaîne de caractères et échapper les apostrophes
        title_val = str(row['title']).replace("'", "''")
        code_val = str(row['code']).replace("'", "''")
        status_val = str(row['status']).replace("'", "''")
        status_txt_val = str(row['status_txt']).replace("'", "''")
        commentaire_val = str(row['commentaire']).replace("'", "''")
        
        insert_stmt = (
            "INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) "
            f"VALUES ('{row['phase_id']}', '{row['level']}', '{title_val}', '{code_val}', '{status_val}', "
            f"'{row['projet_id']}', '{status_txt_val}', {row['status_manuel']}, '{commentaire_val}');\n"
        )
        f.write(insert_stmt)

# Afficher un aperçu des données jointes pour vérification
print("Exemple des données jointes :")
print(df_joined.head())

print("\nFichier SQL généré: insert_phase.sql")

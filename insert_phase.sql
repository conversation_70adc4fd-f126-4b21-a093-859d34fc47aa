INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('5', '3', 'REQUEST FOR QUOTATION (PHASE)', 'P17006-01-RQ', '1', '106', 'CRÉÉ BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('19', '3', 'PREPARATION & CONTRACT (PHASE)', 'P17006-01-PC', '1', '106', 'CRÉÉ BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('33', '3', 'PROJECT EXECUTION (PHASE)', 'P17006-01-PE', '1', '106', 'CRÉÉ BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('47', '3', 'FEASIBILITY STUDY (PHASE)', 'P17006-01-FS', '1', '106', 'CRÉÉ BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('61', '3', 'Design phase _ ATEX Certification', 'P17006-01-DE', '1', '106', 'CRÉÉ BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('75', '3', 'Prototype _ ATEX Certification', 'P17006-01-PR', '1', '106', 'CRÉÉ BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('89', '3', 'QUALIFICATION (PHASE)', 'P17006-01-QT', '1', '106', 'CRÉÉ BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('103', '3', 'INTERFACE ADAPTATION (PHASE)', 'P17006-01-IA', '1', '106', 'CRÉÉ BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('117', '3', 'DEVELOPMENT DOCUMENTATION (PHASE)', 'P17006-01-DD', '1', '106', 'CRÉÉ BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('131', '3', 'SERIES PRODUCTION (PHASE)', 'P17006-01-SP', '1', '106', 'CRÉÉ BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('145', '3', 'INSTALLATION (PHASE)', 'P17006-01-IN', '1', '106', 'CRÉÉ BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('162', '3', 'DATA UPDATE - NA & EX Series Creation', 'P17006-03-DE', '1', '106', 'CRÉÉ BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('178', '3', 'RCA - 71H HT Serie - GtM Issue', 'P17006-04-DE', '1', '106', 'CRÉÉ BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('194', '3', 'CERITIFICATION CSA', 'P17006-05-QT', '1', '106', 'CRÉÉ BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('210', '3', 'IECEX CERTIFICATION', 'P17006-06-DE', '1', '106', 'CRÉÉ BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('224', '3', 'QUALIFICATION (PHASE)', 'P17006-06-QT', '1', '106', 'CRÉÉ BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('238', '3', 'INTERFACE ADAPTATION (PHASE)', 'P17006-06-IA', '1', '106', 'CRÉÉ BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('252', '3', 'DEVELOPMENT DOCUMENTATION (PHASE)', 'P17006-06-DD', '1', '106', 'CRÉÉ BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('268', '3', 'MOD - NOV - CSA Test Report', 'P17006-07-DE', '1', '106', 'CRÉÉ BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('282', '3', 'QUALIFICATION (PHASE)', 'P17006-07-QT', '1', '106', 'CRÉÉ BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('298', '3', 'Glass-Bid Thickness TEST& ENG', 'P17006-08-QT', '1', '106', 'LANC DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('316', '3', 'EX Certif - DOCUMENTATION & TESTING', 'P17006-09-DE', '1', '106', 'LANC DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('332', '3', 'LSR Initiative - DESIGN', 'P17006-10-DE', '1', '106', 'LANC', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('374', '3', 'LSR Initiative - PROTOTYPE', 'P17006-10-PR', '1', '106', 'LANC', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('388', '3', 'LSR Initiative - QUALIFICATION', 'P17006-10-QT', '1', '106', 'LANC', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('404', '3', 'HCRAW - COMPONENT TESTING', 'P17006-11-CT', '1', '106', 'LANC', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('420', '3', 'HCRAW - PROTOTYPE 1', 'P17006-11-P1', '1', '106', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('436', '3', 'HCRAW - QUALIFICATION 1', 'P17006-11-Q1', '1', '106', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('454', '3', 'PO 4501737383 Item 10 Design Change', 'P17006-12-FS', '1', '106', 'LANC DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('470', '3', 'nan', 'P17006-12-CT', '1', '106', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('486', '3', 'nan', 'P17006-12-D1', '1', '106', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('502', '3', 'nan', 'P17006-12-D2', '1', '106', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('518', '3', 'nan', 'P17006-12-P1', '1', '106', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('534', '3', 'nan', 'P17006-12-P2', '1', '106', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('550', '3', 'nan', 'P17006-12-P3', '1', '106', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('566', '3', 'nan', 'P17006-12-Q1', '1', '106', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('582', '3', 'nan', 'P17006-12-Q2', '1', '106', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('598', '3', 'nan', 'P17006-12-Q3', '1', '106', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('614', '3', 'nan', 'P17006-12-FD', '1', '106', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('632', '3', 'CARACTERISATION LSR', 'P17006-13-FS', '1', '106', 'LANC', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('671', '3', 'COMPONENT TESTING', 'P17006-13-CT', '1', '106', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('687', '3', 'DESIGN & ENGINNERING 1', 'P17006-13-D1', '1', '106', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('703', '3', 'DESIGN & ENGINNERING 2', 'P17006-13-D2', '1', '106', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('719', '3', 'PROTOTYPE 1', 'P17006-13-P1', '1', '106', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('735', '3', 'PROTOTYPE 2', 'P17006-13-P2', '1', '106', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('751', '3', 'PROTOTYPE 3', 'P17006-13-P3', '1', '106', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('767', '3', 'QUALIFICATION 1', 'P17006-13-Q1', '1', '106', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('783', '3', 'QUALIFICATION 2', 'P17006-13-Q2', '1', '106', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('799', '3', 'QUALIFICATION 3', 'P17006-13-Q3', '1', '106', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('815', '3', 'FINAL DOCUMENTATION', 'P17006-13-FD', '1', '106', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('833', '3', 'ETUDE ENVERRE SCHOTT', 'P17006-14-FS', '1', '106', 'LANC', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('854', '3', 'COMPONENT TESTING', 'P17006-14-CT', '1', '106', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('870', '3', 'DESIGN & ENGINNERING 1', 'P17006-14-D1', '1', '106', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('886', '3', 'DESIGN & ENGINNERING 2', 'P17006-14-D2', '1', '106', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('902', '3', 'PROTOTYPE 1', 'P17006-14-P1', '1', '106', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('918', '3', 'PROTOTYPE 2', 'P17006-14-P2', '1', '106', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('934', '3', 'PROTOTYPE 3', 'P17006-14-P3', '1', '106', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('950', '3', 'QUALIFICATION 1', 'P17006-14-Q1', '1', '106', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('966', '3', 'QUALIFICATION 2', 'P17006-14-Q2', '1', '106', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('982', '3', 'QUALIFICATION 3', 'P17006-14-Q3', '1', '106', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('998', '3', 'FINAL DOCUMENTATION', 'P17006-14-FD', '1', '106', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('1016', '3', 'ALETIQ - ETUDE DEFINITION', 'P17006-15-D1', '1', '106', 'LANC', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('1032', '3', 'ALETIQ - PREPA ET VAL DONNEES', 'P17006-15-P1', '1', '106', 'LANC', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('1048', '3', 'QUALIFICATION 1', 'P17006-15-Q1', '1', '106', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('1066', '3', 'VALISE_PRESENTATION', 'P17006-16-5', '1', '106', 'LANC DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('1100', '3', '*CLOS SAP TE*', 'P17007-10-RQ', '1', '107', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('1114', '3', '*CLOS SAP TE*', 'P17007-10-PC', '1', '107', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('1128', '3', 'Line 480 - TQPE10 Documentation', 'P17007-10-PE', '1', '107', 'TCLO ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('1142', '3', '*CLOS SAP TE*', 'P17007-10-FS', '1', '107', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('1156', '3', '*CLOS SAP TE*', 'P17007-10-DE', '1', '107', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('1170', '3', '*CLOS SAP TE*', 'P17007-10-PR', '1', '107', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('1184', '3', '*CLOS SAP TE*', 'P17007-10-QT', '1', '107', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('1198', '3', '*CLOS SAP TE*', 'P17007-10-IA', '1', '107', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('1212', '3', '*CLOS SAP TE*', 'P17007-10-DD', '1', '107', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('1226', '3', '*CLOS SAP TE*', 'P17007-10-SP', '1', '107', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('1240', '3', '*CLOS SAP TE*', 'P17007-10-IN', '1', '107', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('1259', '3', 'nan', 'P18004-01-RQ', '1', '112', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('1273', '3', 'nan', 'P18004-01-PC', '1', '112', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('1287', '3', 'QUALIF ADDITIONELLE FENJA', 'P18004-01-PE', '1', '112', 'TCLO IMBL DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('1313', '3', 'VO-1 *CLOS SAP TE*', 'P18004-01-FS', '1', '112', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('1366', '3', 'DESIGN (MA-4) * CLOS SAP TE*', 'P18004-01-DE', '1', '112', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('1380', '3', 'PROTOTYPE REALIZATION (MB-3)*CLOS SAPTE*', 'P18004-01-PR', '1', '112', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('1394', '3', 'QUALIFICATION (MC-4) *CLOS SAP TE*', 'P18004-01-QT', '1', '112', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('1408', '3', 'QUALIF ADDITIONNELLE VO(MC-8)*CLOSSAPTE*', 'P18004-01-IA', '1', '112', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('1422', '3', 'CONTRACT AWARD VO*CLOS SAP TE*', 'P18004-01-DD', '1', '112', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('1436', '3', 'nan', 'P18004-01-SP', '1', '112', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('1450', '3', 'INSTALLATION (PHASE) *CLOS SAP TE*', 'P18004-01-IN', '1', '112', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('1469', '3', 'nan', 'P19004-01-FS', '1', '123', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('1483', '3', 'nan', 'P19004-01-CT', '1', '123', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('1497', '3', '6FO Penetrator dev INDUS_CANCELED', 'P19004-01-D1', '1', '123', 'TCLO ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('1514', '3', '6FO Penetrator testing_CANCELED', 'P19004-01-D2', '1', '123', 'TCLO ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('1533', '3', 'PROTOTYPES ASSEMBLY_CANCELED', 'P19004-01-P1', '1', '123', 'TCLO ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('1598', '3', '6FO Penetrator test_CANCELED', 'P19004-01-P2', '1', '123', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('1612', '3', 'QUALIF_15/17/18 &VO 9-10/11-12_CANCELED', 'P19004-01-P3', '1', '123', 'TCLO ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('1644', '3', 'QUALIF_XT Dry Mate Flange_CANCELED', 'P19004-01-Q1', '1', '123', 'TCLO ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('1671', '3', 'QUALIF_TH/XT WetMate 1_CANCELED', 'P19004-01-Q2', '1', '123', 'LANC BLOQ ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('1694', '3', 'QUALIF_TH/XT WetMate 2_CANCELED', 'P19004-01-Q3', '1', '123', 'LANC BLOQ ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('1723', '3', 'FINAL DOCUMENTATION_CANCELED', 'P19004-01-FD', '1', '123', 'LANC BLOQ ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('1744', '3', 'REQUEST FOR QUOTATION (PHASE)', 'P19005-01-RQ', '1', '124', 'CRÉÉ IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('1758', '3', 'PREPARATION & CONTRACT (PHASE)', 'P19005-01-PC', '1', '124', 'CRÉÉ IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('1772', '3', 'PROJECT EXECUTION (PHASE)', 'P19005-01-PE', '1', '124', 'CRÉÉ IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('1786', '3', 'FEASIBILITY STUDY (PHASE)', 'P19005-01-FS', '1', '124', 'CRÉÉ IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('1839', '3', 'DESIGN (PHASE)', 'P19005-01-DE', '1', '124', 'CRÉÉ IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('1853', '3', 'PROTOTYPE REALIZATION (PHASE)', 'P19005-01-PR', '1', '124', 'CRÉÉ IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('1867', '3', 'QUALIFICATION (PHASE)', 'P19005-01-QT', '1', '124', 'CRÉÉ IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('1881', '3', 'INTERFACE ADAPTATION (PHASE)', 'P19005-01-IA', '1', '124', 'CRÉÉ IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('1895', '3', 'DEVELOPMENT DOCUMENTATION (PHASE)', 'P19005-01-DD', '1', '124', 'CRÉÉ IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('1909', '3', 'SERIES PRODUCTION (PHASE)', 'P19005-01-SP', '1', '124', 'CRÉÉ IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('1923', '3', 'INSTALLATION (PHASE)', 'P19005-01-IN', '1', '124', 'CRÉÉ IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('1940', '3', 'TEMPORAIRE PO 4501729439', 'P19005-02-RQ', '1', '124', 'TCLO IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('1962', '3', 'nan', 'P19005-02-PC', '1', '124', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('1976', '3', 'nan', 'P19005-02-PE', '1', '124', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('1990', '3', 'nan', 'P19005-02-FS', '1', '124', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('2043', '3', 'nan', 'P19005-02-DE', '1', '124', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('2057', '3', 'nan', 'P19005-02-PR', '1', '124', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('2071', '3', 'nan', 'P19005-02-QT', '1', '124', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('2085', '3', '4504090384_Qty 3 Contacts', 'P19005-02-IA', '1', '124', 'TCLO IMBL ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('2104', '3', '4503774077-VO2 *CLOS SAP TE*', 'P19005-02-DD', '1', '124', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('2118', '3', 'Asgard Ph2 - Pen. Ceramic *CLOS SAP TE*', 'P19005-02-SP', '1', '124', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('2132', '3', 'OSI at MAN *CLOS SAP TE*', 'P19005-02-IN', '1', '124', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('2148', '3', 'OSI 603 *CLOS SAP TE*', 'P19005-03-O1', '1', '124', 'CRÉÉ IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('2162', '3', 'OSI 605 *CLOS SAP TE*', 'P19005-03-O2', '1', '124', 'CRÉÉ IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('2176', '3', 'OSI 604 - Asgard HV ceramic assembly', 'P19005-03-O3', '1', '124', 'TCLO IMBL REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('2205', '3', 'OSI 610', 'P19005-04-O1', '1', '124', 'LANC IMBL ACCE', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('2221', '3', 'OSI 615 - Asgard AMB PT100 at MAN', 'P19005-04-O2', '1', '124', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('2237', '3', 'OSI 607', 'P19005-04-O3', '1', '124', 'CRÉÉ IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('2255', '3', 'OSI 608-609 - ASGARD - Test Jumpers #1', 'P19005-05-O1', '1', '124', 'TCLO IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('2276', '3', 'OSI 655 - ASGARD - Test Jumpers #2', 'P19005-05-O2', '1', '124', 'TCLO IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('2292', '3', 'OSI 659 - ASGARD - KRISTIANSUND', 'P19005-05-O3', '1', '124', 'TCLO IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('2312', '3', 'nan', 'P19009-01-RQ', '1', '128', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('2326', '3', 'nan', 'P19009-01-PC', '1', '128', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('2340', '3', 'nan', 'P19009-01-PE', '1', '128', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('2354', '3', 'nan', 'P19009-01-FS', '1', '128', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('2407', '3', 'nan', 'P19009-01-DE', '1', '128', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('2421', '3', 'DESIGN PROTOTYPE PRE-TESTS *CLOS SAP TE*', 'P19009-01-PR', '1', '128', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('2435', '3', 'PO N78840 - Amdt 1 - 20 Hot Splices', 'P19009-01-QT', '1', '128', 'TCLO IMBL DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('2492', '3', 'VO-02 - POLLUTED SPLICE QUALIF', 'P19009-01-IA', '1', '128', 'TCLO IMBL DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('2508', '3', 'PO 027783R919P845 - XRay examination', 'P19009-01-DD', '1', '128', 'TCLO IMBL ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('2522', '3', 'nan', 'P19009-01-SP', '1', '128', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('2536', '3', 'PO 027783R918P881-Mock up & tooling kit', 'P19009-01-IN', '1', '128', 'TCLO IMBL ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('2574', '3', 'nan', 'P20003-01-RQ', '1', '132', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('2588', '3', 'CO-012 _ *Clos SAP TE*', 'P20003-01-PC', '1', '132', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('2602', '3', 'CO-013 _ *Clos SAP TE*', 'P20003-01-PE', '1', '132', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('2616', '3', 'CO-014 _ *Clos SAP TE*', 'P20003-01-FS', '1', '132', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('2669', '3', 'PROJECT EXECUTION 1/4_*Clos SAP TE*', 'P20003-01-DE', '1', '132', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('2683', '3', 'PROJECT EXECUTION 2/4_*Clos SAP TE*', 'P20003-01-PR', '1', '132', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('2697', '3', 'PROJECT EXECUTION 3/4_*Clos SAP TE*', 'P20003-01-QT', '1', '132', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('2711', '3', 'PROJECT EXECUTION 4/4_*Clos SAP TE*', 'P20003-01-IA', '1', '132', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('2725', '3', 'C0-006/CO-008/CO-011_*Clos SAP TE*', 'P20003-01-DD', '1', '132', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('2739', '3', 'OSI 602 - CO-009 EDB_*Clos SAP TE*', 'P20003-01-SP', '1', '132', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('2753', '3', 'OSI 601 - CO-010 ODB_*Clos SAP TE*', 'P20003-01-IN', '1', '132', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('2770', '3', 'nan', 'P20003-05-RQ', '1', '132', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('2784', '3', 'nan', 'P20003-05-PC', '1', '132', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('2798', '3', 'nan', 'P20003-05-PE', '1', '132', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('2812', '3', 'nan', 'P20003-05-FS', '1', '132', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('2865', '3', 'ODB PENETRATORS VALIDATION', 'P20003-05-DE', '1', '132', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('2897', '3', 'ODB validation soudure FE', 'P20003-05-PR', '1', '132', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('2911', '3', 'EDB Qualif Nickel plating _ QPP 373-070', 'P20003-05-QT', '1', '132', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('2925', '3', 'EDB LSR BOOTS', 'P20003-05-IA', '1', '132', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('2939', '3', 'EDB Remplissage bille céramiques', 'P20003-05-DD', '1', '132', 'TCLO IMBL ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('2953', '3', 'nan', 'P20003-05-SP', '1', '132', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('2967', '3', 'nan', 'P20003-05-IN', '1', '132', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('2983', '3', 'nan', 'P20003-07-RQ', '1', '132', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('2997', '3', 'nan', 'P20003-07-PC', '1', '132', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('3011', '3', 'SAV 21-002_CO-015 *SAP TE CLOS*', 'P20003-07-PE', '1', '132', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('3025', '3', 'nan', 'P20003-07-FS', '1', '132', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('3078', '3', 'nan', 'P20003-07-DE', '1', '132', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('3092', '3', 'nan', 'P20003-07-PR', '1', '132', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('3106', '3', 'nan', 'P20003-07-QT', '1', '132', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('3120', '3', 'nan', 'P20003-07-IA', '1', '132', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('3134', '3', 'nan', 'P20003-07-DD', '1', '132', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('3148', '3', 'nan', 'P20003-07-SP', '1', '132', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('3162', '3', 'nan', 'P20003-07-IN', '1', '132', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('3180', '3', 'nan', 'P20006-01-RQ', '1', '135', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('3194', '3', 'nan', 'P20006-01-PC', '1', '135', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('3208', '3', 'EARLY ENGAGEMENT - M1', 'P20006-01-PE', '1', '135', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('3222', '3', 'EARLY ENGAGEMENT - M2', 'P20006-01-FS', '1', '135', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('3275', '3', 'EARLY ENGAGEMENT - M3', 'P20006-01-DE', '1', '135', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('3289', '3', 'nan', 'P20006-01-PR', '1', '135', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('3303', '3', 'nan', 'P20006-01-QT', '1', '135', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('3317', '3', 'nan', 'P20006-01-IA', '1', '135', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('3331', '3', 'nan', 'P20006-01-DD', '1', '135', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('3345', '3', 'nan', 'P20006-01-SP', '1', '135', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('3359', '3', 'nan', 'P20006-01-IN', '1', '135', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('3376', '3', 'PO4500914864 - It_141 - COR-005 - Clos', 'P20006-02-RQ', '1', '135', 'TCLO ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('3390', '3', 'PO4500914864 - It_151 - COR-004 - Clos', 'P20006-02-PC', '1', '135', 'TCLO ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('3404', '3', 'PO4500914864 - It_11 - NRE Umb - Clos', 'P20006-02-PE', '1', '135', 'TCLO ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('3519', '3', 'PO4500914864 - It_161_NRE ROV SC - Clos', 'P20006-02-FS', '1', '135', 'TCLO ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('3541', '3', 'PO4500914864 - It_41 - NRE COR-002 -Clos', 'P20006-02-DE', '1', '135', 'TCLO IMBL ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('3561', '3', 'Outillages - Assemblage', 'P20006-02-PR', '1', '135', 'LANC', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('4208', '3', 'Outillages - Laboratoire', 'P20006-02-QT', '1', '135', 'LANC', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('4258', '3', 'Spare parts', 'P20006-02-IA', '1', '135', 'LANC', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('4419', '3', 'TRAINING - HV penetrator tool - Clos', 'P20006-02-DD', '1', '135', 'TCLO ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('4435', '3', 'PO4500914864 - Produits', 'P20006-02-SP', '1', '135', 'LANC ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('4939', '3', 'PO4500914864 - It_101 - COR-007 - Clos', 'P20006-02-IN', '1', '135', 'TCLO ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('4957', '3', 'Jansz-Suivi projet-Q1 2021 *CLOS SAP TE*', 'P20006-03-RQ', '1', '135', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('4971', '3', 'Jansz-Suivi projet-Q2 2021 *CLOS SAP TE*', 'P20006-03-PC', '1', '135', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('4985', '3', 'Jansz-Suivi projet-Q3 2021 *CLOS SAP TE*', 'P20006-03-PE', '1', '135', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('4999', '3', 'Jansz-Suivi projet-Q4 2021 *CLOS SAP TE*', 'P20006-03-FS', '1', '135', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('5013', '3', 'Jansz-Suivi projet-Q1 2022 *CLOS SAP TE*', 'P20006-03-DE', '1', '135', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('5027', '3', 'Jansz - Suivi projet - Q2 2022 - Clos', 'P20006-03-PR', '1', '135', 'TCLO ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('5139', '3', 'Jansz - Suivi projet - Q3 2022 - Clos', 'P20006-03-QT', '1', '135', 'TCLO ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('5155', '3', 'Jansz - Suivi projet - Q4 2022 - Clos', 'P20006-03-IA', '1', '135', 'TCLO ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('5171', '3', 'Jansz - Suivi projet - Q1 2023 - Clos', 'P20006-03-DD', '1', '135', 'TCLO ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('5187', '3', 'Jansz - Workshop Vaasa', 'P20006-03-SP', '1', '135', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('5202', '3', 'OSI 616 - 1er compresseur MAN', 'P20006-03-IN', '1', '135', 'TCLO ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('5220', '3', 'COR-017 - PO Line 07 - Scope 1- Clos', 'P20006-04-FS', '1', '135', 'TCLO DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('5236', '3', 'COR-017 - Calcareous growth_Test - Clos', 'P20006-04-CT', '1', '135', 'TCLO ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('5266', '3', 'ADDIX - Backup LSR item 4 et 7 - Clos', 'P20006-04-D1', '1', '135', 'TCLO DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('5344', '3', 'COR-001 - clef HYTORQ - Clos', 'P20006-04-D2', '1', '135', 'TCLO IMBL ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('5360', '3', 'Workshop Egersund', 'P20006-04-P1', '1', '135', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('5376', '3', 'SAV 23-049 - Penetrator He Leak - Clos', 'P20006-04-P2', '1', '135', 'TCLO IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('5392', '3', 'Outillages SCM pour OSI - Clos', 'P20006-04-P3', '1', '135', 'TCLO IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('5413', '3', 'OSI 617 - Item 15 - Clos', 'P20006-04-Q1', '1', '135', 'TCLO ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('5429', '3', 'OSI 631 - 2eme compresseur MAN - Clos', 'P20006-04-Q2', '1', '135', 'TCLO ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('5445', '3', 'COR-020_It 1_NRE_Vib_M1_Proto - Clos', 'P20006-04-Q3', '1', '135', 'LANC IMBL ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('5469', '3', 'COR_PO4500230157_HITACHI - Bolts - Clos', 'P20006-04-FD', '1', '135', 'TCLO ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('5491', '3', 'COR-020_Item 1_NRE_Vibration_M2_Test', 'P20006-04-Q4', '1', '135', 'LANC ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('5509', '3', 'PO Line 20_MAN_KIT OSI 0025 - Clos', 'P20006-07-O1', '1', '135', 'LANC IMBL ACCE', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('5528', '3', 'PO Line 10_HITACHI_KIT OSI 0026 - Clos', 'P20006-07-O2', '1', '135', 'TCLO ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('5547', '3', 'PO Line 30_EGERSUND_KIT OSI 0027 - Clos', 'P20006-07-O3', '1', '135', 'TCLO ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('5568', '3', 'OSI 618', 'P20006-06-01', '1', '135', 'TCLO ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('5584', '3', 'OSI 619', 'P20006-06-02', '1', '135', 'TCLO ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('5600', '3', 'OSI 630', 'P20006-06-03', '1', '135', 'TCLO DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('5616', '3', 'OSI 661', 'P20006-06-04', '1', '135', 'TCLO ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('5632', '3', 'OSI 632', 'P20006-06-05', '1', '135', 'TCLO ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('5648', '3', 'OSI 633', 'P20006-06-06', '1', '135', 'TCLO ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('5664', '3', 'OSI 634', 'P20006-06-07', '1', '135', 'TCLO ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('5680', '3', 'OSI 635', 'P20006-06-08', '1', '135', 'LANC DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('5696', '3', 'OSI 636', 'P20006-06-09', '1', '135', 'TCLO ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('5712', '3', 'OSI 637', 'P20006-06-10', '1', '135', 'TCLO ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('5728', '3', 'OSI 686 - Egersund', 'P20006-06-70', '1', '135', 'LANC DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('5744', '3', 'OSI 667 - Vaasa_Transfo1_Items 2-4-6', 'P20006-06-40', '1', '135', 'TCLO ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('5776', '3', 'OSI 679', 'P20006-06-11', '1', '135', 'TCLO ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('5792', '3', 'OSI 618_bis', 'P20006-06-12', '1', '135', 'TCLO ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('5808', '3', 'OSI 641', 'P20006-06-13', '1', '135', 'TCLO ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('5824', '3', 'OSI 642', 'P20006-06-14', '1', '135', 'TCLO ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('5840', '3', 'OSI 643', 'P20006-06-15', '1', '135', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('5856', '3', 'OSI 668 - Vaasa_Transfo1_Items 7-13', 'P20006-06-41', '1', '135', 'TCLO ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('5872', '3', 'OSI 680', 'P20006-06-16', '1', '135', 'TCLO ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('5888', '3', 'OSI 685', 'P20006-06-17', '1', '135', 'TCLO ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('5904', '3', 'OSI 681', 'P20006-06-18', '1', '135', 'TCLO ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('5920', '3', 'OSI 688', 'P20006-06-19', '1', '135', 'LANC DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('5936', '3', 'OSI 689', 'P20006-06-20', '1', '135', 'TCLO IMBL ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('5952', '3', 'OSI 646 - MAN_Unit 5 Item 11 instal', 'P20006-06-21', '1', '135', 'LANC DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('5968', '3', 'OSI 647 - MAN_Unit 5 oil filling & conf3', 'P20006-06-22', '1', '135', 'LANC DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('5984', '3', 'OSI 638 - MAN_Unit 3 Item 14 instal', 'P20006-06-23', '1', '135', 'LANC DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('6000', '3', 'OSI 639 - MAN', 'P20006-06-24', '1', '135', 'LANC DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('6016', '3', 'OSI 648 - MAN', 'P20006-06-25', '1', '135', 'LANC DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('6032', '3', 'OSI 649 - MAN', 'P20006-06-26', '1', '135', 'LANC DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('6048', '3', 'OSI xxx - MAN', 'P20006-06-27', '1', '135', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('6064', '3', 'OSI xxx - MAN', 'P20006-06-28', '1', '135', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('6080', '3', 'OSI xxx - MAN', 'P20006-06-29', '1', '135', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('6096', '3', 'OSI xxx - MAN', 'P20006-06-30', '1', '135', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('6112', '3', 'OSI xxx - MAN', 'P20006-06-31', '1', '135', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('6128', '3', 'OSI xxx - MAN', 'P20006-06-32', '1', '135', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('6144', '3', 'OSI xxx - MAN', 'P20006-06-33', '1', '135', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('6160', '3', 'OSI xxx - MAN', 'P20006-06-34', '1', '135', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('6176', '3', 'OSI xxx - MAN', 'P20006-06-35', '1', '135', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('6192', '3', 'OSI xxx - MAN', 'P20006-06-36', '1', '135', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('6208', '3', 'OSI xxx - MAN', 'P20006-06-37', '1', '135', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('6224', '3', 'OSI xxx - MAN', 'P20006-06-38', '1', '135', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('6240', '3', 'OSI xxx - MAN', 'P20006-06-39', '1', '135', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('6256', '3', 'OSI 670 - Vaasa', 'P20006-06-42', '1', '135', 'LANC DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('6272', '3', 'OSI 691 - Vaasa', 'P20006-06-43', '1', '135', 'LANC DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('6288', '3', 'OSI 692 - Vaasa', 'P20006-06-44', '1', '135', 'LANC DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('6304', '3', 'OSI 695 - Vaasa', 'P20006-06-45', '1', '135', 'LANC DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('6323', '3', 'OSI 669 - Vaasa', 'P20006-06-46', '1', '135', 'LANC DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('6339', '3', 'OSI 701 - Vaasa', 'P20006-06-47', '1', '135', 'LANC DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('6355', '3', 'OSI xxx - Vaasa', 'P20006-06-48', '1', '135', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('6371', '3', 'OSI xxx - Vaasa', 'P20006-06-49', '1', '135', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('6387', '3', 'OSI xxx - Vaasa', 'P20006-06-50', '1', '135', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('6403', '3', 'OSI xxx - Vaasa', 'P20006-06-51', '1', '135', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('6419', '3', 'OSI xxx - Vaasa', 'P20006-06-52', '1', '135', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('6435', '3', 'OSI xxx - Vaasa', 'P20006-06-53', '1', '135', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('6451', '3', 'OSI xxx - Vaasa', 'P20006-06-54', '1', '135', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('6467', '3', 'OSI xxx - Vaasa', 'P20006-06-55', '1', '135', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('6483', '3', 'OSI xxx - Vaasa', 'P20006-06-56', '1', '135', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('6499', '3', 'OSI xxx - Vaasa', 'P20006-06-57', '1', '135', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('6515', '3', 'OSI xxx - Vaasa', 'P20006-06-58', '1', '135', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('6531', '3', 'OSI xxx - Vaasa', 'P20006-06-59', '1', '135', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('6547', '3', 'OSI xxx - Vaasa', 'P20006-06-60', '1', '135', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('6563', '3', 'OSI xxx - Vaasa', 'P20006-06-61', '1', '135', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('6579', '3', 'OSI xxx - Vaasa', 'P20006-06-62', '1', '135', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('6595', '3', 'OSI xxx - Vaasa', 'P20006-06-63', '1', '135', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('6611', '3', 'OSI xxx - Vaasa', 'P20006-06-64', '1', '135', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('6627', '3', 'OSI xxx - Vaasa', 'P20006-06-65', '1', '135', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('6643', '3', 'OSI xxx - Vaasa', 'P20006-06-66', '1', '135', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('6659', '3', 'OSI xxx - Vaasa', 'P20006-06-67', '1', '135', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('6675', '3', 'OSI xxx - Vaasa', 'P20006-06-68', '1', '135', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('6691', '3', 'OSI xxx - Vaasa', 'P20006-06-69', '1', '135', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('6707', '3', 'OSI 665 - Egersund', 'P20006-06-71', '1', '135', 'LANC DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('6723', '3', 'OSI 696 - Egersund', 'P20006-06-72', '1', '135', 'LANC DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('6739', '3', 'OSI 699 - Egersund', 'P20006-06-73', '1', '135', 'LANC DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('6755', '3', 'OSI 700 - Egersund', 'P20006-06-74', '1', '135', 'LANC DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('6771', '3', 'OSI xxx - Egersund', 'P20006-06-75', '1', '135', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('6787', '3', 'OSI xxx - Egersund', 'P20006-06-76', '1', '135', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('6805', '3', 'SAV 23-115 (Item 14_Test jumper at MAN)', 'P20006-08-S1', '1', '135', 'TCLO IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('6880', '3', 'SAV 23-121 (It 2_Low IR_HITACHI) -Clos', 'P20006-08-S2', '1', '135', 'TCLO IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('7018', '3', 'SAV 24-068 (MAN_Motor Unit 4 fall down)', 'P20006-08-S3', '1', '135', 'LANC ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('7196', '3', 'SAV 24-072_ITEM 4_PD Issue at HITACHI', 'P20006-08-S4', '1', '135', 'LANC DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('7218', '3', 'SAV 24-084_ITEM 11_Membrane puce', 'P20006-08-S5', '1', '135', 'LANC DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('7236', '3', 'COR-021_NRE_Item25_Deployment Caps_18kV', 'P20006-05-FS', '1', '135', 'LANC ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('7280', '3', 'COR-021_NRE_Item 26_Deployment Caps_6kV', 'P20006-05-CT', '1', '135', 'LANC ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('7304', '3', 'COR-021_Delivery Items 25, 26', 'P20006-05-D1', '1', '135', 'LANC ACCE DELN NORD', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('7328', '3', 'PO 4501787725 - Line 27 - FILC Produits', 'P20006-05-D2', '1', '135', 'LANC ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('7351', '3', 'PO 4501787725 - Line 28 - FILC CAPEX', 'P20006-05-P1', '1', '135', 'LANC ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('7367', '3', 'PO 4501787725 - Line 29 - FILC NRE', 'P20006-05-P2', '1', '135', 'LANC ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('7383', '3', 'COR_029 - Lightening test', 'P20006-05-P3', '1', '135', 'LANC ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('7437', '3', 'COR_030 - HISC analysis', 'P20006-05-Q1', '1', '135', 'LANC DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('7453', '3', 'COR-028 - LSR Socket - NRE & CAPEX', 'P20006-05-Q2', '1', '135', 'LANC ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('7469', '3', 'COR-028 - LSR Socket - Item 28 (x12)', 'P20006-05-Q3', '1', '135', 'LANC ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('7487', '3', 'Libre', 'P20006-05-FD', '1', '135', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('7505', '3', 'PO 4500914864 - COR102_COR018_LSR - Clos', 'P20006-09-FS', '1', '135', 'TCLO IMBL ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('7521', '3', 'PO 4500914864 - Line042_COR022_Clos', 'P20006-09-CT', '1', '135', 'TCLO IMBL ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('7537', '3', 'PO 4501787725 - COR027_EB Joints - Clos', 'P20006-09-D1', '1', '135', 'TCLO IMBL ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('7563', '3', 'PO xxx - COR xxx - Outillage Item 9', 'P20006-09-D2', '1', '135', 'LANC', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('7579', '3', 'nan', 'P20006-09-P1', '1', '135', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('7595', '3', 'nan', 'P20006-09-P2', '1', '135', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('7611', '3', 'nan', 'P20006-09-P3', '1', '135', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('7627', '3', 'nan', 'P20006-09-Q1', '1', '135', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('7643', '3', 'nan', 'P20006-09-Q2', '1', '135', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('7659', '3', 'nan', 'P20006-09-Q3', '1', '135', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('7675', '3', 'nan', 'P20006-09-FD', '1', '135', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('7695', '3', 'nan', 'P21001-01-RQ', '1', '137', 'TCLO IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('7709', '3', 'nan', 'P21001-01-PC', '1', '137', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('7723', '3', 'nan', 'P21001-01-PE', '1', '137', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('7737', '3', 'nan', 'P21001-01-FS', '1', '137', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('7790', '3', 'DESIGN (PHASE) *CLOS SAP TE*', 'P21001-01-DE', '1', '137', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('7804', '3', 'KRAFLA LV - 3.1 PROTOTYPES', 'P21001-01-PR', '1', '137', 'LANC BLOQ ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('7879', '3', 'KRAFLA LV 3.2 TOOLING & ITS DESIGN', 'P21001-01-QT', '1', '137', 'LANC IMBL ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('7893', '3', 'KRAFLA LV 4.1 ATEX QUALIFICATION', 'P21001-01-IA', '1', '137', 'TCLO ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('7907', '3', 'KRAFLA LV 4.2 EQUINOR QUALIFICATION', 'P21001-01-DD', '1', '137', 'TCLO IMBL ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('7921', '3', 'nan', 'P21001-01-SP', '1', '137', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('7935', '3', 'nan', 'P21001-01-IN', '1', '137', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('7954', '3', 'FEED STUDY IN AIR', 'P21002-01-FS', '1', '138', 'TCLO IMBL ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('7968', '3', 'FIELD EXPANDER - Prototypes', 'P21002-01-CT', '1', '138', 'LANC IMBL ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('8047', '3', 'DESIGN (PHASE 1) *CLOS SAP TE*', 'P21002-01-D1', '1', '138', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('8061', '3', 'PHASE 2 : DESIGN & ENGINEERING', 'P21002-01-D2', '1', '138', 'TCLO IMBL ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('8075', '3', 'PHASE 3.1 : Prototypes Penetrator', 'P21002-01-P1', '1', '138', 'TCLO IMBL ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('8129', '3', 'PHASE 3.2 : Tooling', 'P21002-01-P2', '1', '138', 'TCLO IMBL ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('8185', '3', 'FiELD EXPANDER - 2nd Qualification & OSI', 'P21002-01-P3', '1', '138', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('8199', '3', 'PHASE 4 : QUALIFICATION 1/2', 'P21002-01-Q1', '1', '138', 'TCLO IMBL ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('8237', '3', 'VO-01 VIBRATION TESTING', 'P21002-01-Q2', '1', '138', 'TCLO IMBL ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('8251', '3', 'PHASE 4 : QUALIFICATION 2/2', 'P21002-01-Q3', '1', '138', 'TCLO IMBL ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('8265', '3', 'PHASE 5 : FINAL DOCUMENTATION', 'P21002-01-FD', '1', '138', 'TCLO IMBL ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('9606', '3', 'nan', 'P21006-01-RQ', '1', '143', 'CRÉÉ IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('9620', '3', 'nan', 'P21006-01-PC', '1', '143', 'CRÉÉ IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('9634', '3', 'nan', 'P21006-01-PE', '1', '143', 'CRÉÉ IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('9648', '3', 'nan', 'P21006-01-FS', '1', '143', 'CRÉÉ IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('9701', '3', 'DESIGN (PHASE)', 'P21006-01-DE', '1', '143', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('9721', '3', 'PROTOTYPE REALIZATION (PHASE)', 'P21006-01-PR', '1', '143', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('9847', '3', 'QUALIFICATION (PHASE)', 'P21006-01-QT', '1', '143', 'LANC DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('9927', '3', 'nan', 'P21006-01-IA', '1', '143', 'CRÉÉ IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('9941', '3', 'nan', 'P21006-01-DD', '1', '143', 'CRÉÉ IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('9955', '3', 'nan', 'P21006-01-SP', '1', '143', 'CRÉÉ IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('9969', '3', 'nan', 'P21006-01-IN', '1', '143', 'CRÉÉ IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('9988', '3', 'nan', 'P21007-01-RQ', '1', '144', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('10002', '3', 'nan', 'P21007-01-PC', '1', '144', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('10016', '3', 'PROJECT EXECUTION', 'P21007-01-PE', '1', '144', 'TCLO IMBL ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('10032', '3', 'nan', 'P21007-01-FS', '1', '144', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('10085', '3', 'nan', 'P21007-01-DE', '1', '144', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('10099', '3', 'nan', 'P21007-01-PR', '1', '144', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('10113', '3', 'nan', 'P21007-01-QT', '1', '144', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('10127', '3', 'nan', 'P21007-01-IA', '1', '144', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('10141', '3', 'nan', 'P21007-01-DD', '1', '144', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('10155', '3', 'nan', 'P21007-01-SP', '1', '144', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('10169', '3', 'nan', 'P21007-01-IN', '1', '144', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('9104', '3', 'OSI PT100 MAN', 'P21005-01-RQ', '1', '145', 'TCLO ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('9170', '3', 'PO8024877 - VPI protective cap x4', 'P21005-01-PC', '1', '145', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('9184', '3', 'JANSZ AMB - Support Projet - Period 1', 'P21005-01-PE', '1', '145', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('9198', '3', 'JANSZ AMB - Support Projet - Period 2', 'P21005-01-FS', '1', '145', 'TCLO IMBL ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('9214', '3', 'JANSZ AMB - Support Projet - Period 3', 'P21005-01-DE', '1', '145', 'TCLO ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('9228', '3', 'JANSZ AMB-Support Projet-Test jumper P1', 'P21005-01-PR', '1', '145', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('9242', '3', 'JANSZ AMB-Support Projet-Test jumper P2', 'P21005-01-QT', '1', '145', 'TCLO IMBL ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('9256', '3', 'PO 4973512 - DUMMY Penetrator VPI', 'P21005-01-IA', '1', '145', 'TCLO IMBL ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('9284', '3', 'JANSZ AMB-Support Projet-Test jumper P3', 'P21005-01-DD', '1', '145', 'LANC BLOQ ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('9298', '3', 'SERIES PRODUCTION (PHASE)', 'P21005-01-SP', '1', '145', 'LANC IMBL ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('9506', '3', 'PO 712/8010963 - Additional Marking', 'P21005-01-IN', '1', '145', 'TCLO IMBL ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('9523', '3', 'SAV 1', 'P21005-02-S1', '1', '145', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('9563', '3', 'SAV 23-095 Test Jumper', 'P21005-01-S2', '1', '145', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('9586', '3', 'SAV 3', 'P21005-01-S3', '1', '145', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('10188', '3', 'nan', 'P21008-01-RQ', '1', '146', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('10202', '3', 'nan', 'P21008-01-PC', '1', '146', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('10216', '3', 'nan', 'P21008-01-PE', '1', '146', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('10230', '3', 'SAV 21-019_R-TEST SN01 * SAP TE CLOS *', 'P21008-01-FS', '1', '146', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('10283', '3', 'SAV 21-019_R-TEST SN02 * SAP TE CLOS *', 'P21008-01-DE', '1', '146', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('10297', '3', 'SAV 21-020_P-TEST SN02', 'P21008-01-PR', '1', '146', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('10318', '3', 'SAV 21-022_R-TEST SN03 * SAP TE CLOS *', 'P21008-01-QT', '1', '146', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('10332', '3', 'SAV 21-045_P-TEST SN03 * SAP TE CLOS *', 'P21008-01-IA', '1', '146', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('10346', '3', 'SAV 22-010_P-TEST SN04', 'P21008-01-DD', '1', '146', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('10371', '3', 'nan', 'P21008-01-SP', '1', '146', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('10385', '3', 'nan', 'P21008-01-IN', '1', '146', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('10404', '3', 'CONCEPT DESIGN', 'P22001-01-FS', '1', '147', 'TCLO BLOQ ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('10477', '3', 'nan', 'P22001-01-CT', '1', '147', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('10491', '3', 'DESIGN & ENGINNERING 1', 'P22001-01-D1', '1', '147', 'TCLO BLOQ ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('10522', '3', 'DESIGN & ENGINEERING EARTH CONNECTOR', 'P22001-01-D2', '1', '147', 'TCLO BLOQ ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('10541', '3', 'PROTOTYPE 1', 'P22001-01-P1', '1', '147', 'TCLO BLOQ ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('10784', '3', 'VO 03_Proto delivery MQC testing', 'P22001-01-P2', '1', '147', 'LANC ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('10895', '3', 'nan', 'P22001-01-P3', '1', '147', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('10909', '3', 'QUALIFICATION 1', 'P22001-01-Q1', '1', '147', 'TCLO IMBL ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('11105', '3', 'nan', 'P22001-01-Q2', '1', '147', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('11119', '3', 'nan', 'P22001-01-Q3', '1', '147', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('11133', '3', 'DNV certificat final Power & Optical', 'P22001-01-FD', '1', '147', 'LANC ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('11152', '3', 'Concept Validation TRL2 _ CLOS', 'P22002-01-FS', '1', '148', 'TCLO BLOQ ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('11199', '3', 'Alternative Silicone Investigation_CLOS', 'P22002-01-CT', '1', '148', 'TCLO BLOQ ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('11227', '3', 'DESIGN & ENGINEERING_CLOS', 'P22002-01-D1', '1', '148', 'TCLO BLOQ ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('11296', '3', 'nan', 'P22002-01-D2', '1', '148', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('11310', '3', 'SPLICE PROTOTYPE MANUFACTURING', 'P22002-01-P1', '1', '148', 'TCLO IMBL ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('11423', '3', 'STAR END PROTOTYPE MANUFACTURING', 'P22002-01-P2', '1', '148', 'TCLO IMBL ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('11508', '3', 'SPLICE Prototypes x18', 'P22002-01-P3', '1', '148', 'LANC DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('11533', '3', 'QUALIFICATION', 'P22002-01-Q1', '1', '148', 'LANC ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('11593', '3', 'THERMAL AGEING TEST', 'P22002-01-Q2', '1', '148', 'LANC ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('11608', '3', 'TOOLS QUALIFICATION', 'P22002-01-Q3', '1', '148', 'LANC ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('11622', '3', 'FINAL DOCUMENTATION', 'P22002-01-FD', '1', '148', 'LANC IMBL ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('11644', '3', 'CONCEPT DESIGN', 'P22003-01-FS', '1', '149', 'TCLO BLOQ ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('11715', '3', 'nan', 'P22003-01-CT', '1', '149', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('11729', '3', 'DESIGN & ENGINNERING 1', 'P22003-01-D1', '1', '149', 'TCLO BLOQ ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('11762', '3', 'nan', 'P22003-01-D2', '1', '149', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('11776', '3', 'PROTOTYPE 1', 'P22003-01-P1', '1', '149', 'TCLO BLOQ ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('11921', '3', 'LIVRAISON PROTO', 'P22003-01-P2', '1', '149', 'TCLO BLOQ ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('11954', '3', 'nan', 'P22003-01-P3', '1', '149', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('11968', '3', 'QUALIFICATION 1', 'P22003-01-Q1', '1', '149', 'TCLO BLOQ ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('12126', '3', 'nan', 'P22003-01-Q2', '1', '149', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('12140', '3', 'nan', 'P22003-01-Q3', '1', '149', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('12154', '3', 'nan', 'P22003-01-FD', '1', '149', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('12173', '3', 'SERIE Support', 'P22004-01-SP', '1', '150', 'LANC DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('12246', '3', 'SERIE Project Execution', 'P22004-01-PE', '1', '150', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('12260', '3', 'SERIE DP Investigation & Fine Tuning', 'P22004-01-PA', '1', '150', 'LANC', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('12279', '3', 'SERIE PO', 'P22005-01-SP', '1', '151', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('12299', '3', 'SERIE Project Execution', 'P22005-01-PE', '1', '151', 'CRÉÉ IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('12313', '3', 'SERIE Spare parts', 'P22005-01-PA', '1', '151', 'CRÉÉ IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('12330', '3', 'PO Item 20 - H3 Refurbishment', 'P22005-02-SP', '1', '151', 'TCLO IMBL ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('12374', '3', 'PO Item 10 - H3 Dismantling', 'P22005-02-PE', '1', '151', 'TCLO IMBL ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('12388', '3', 'PO Item 30 - New Hose Procurement', 'P22005-02-PA', '1', '151', 'TCLO IMBL ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('12412', '3', 'OSI 620 - Egersund H3 dismantling', 'P22005-05-O1', '1', '151', 'TCLO IMBL ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('12428', '3', 'OSI 666_MAN_Nettoyage étain pénétrateur', 'P22005-05-O2', '1', '151', 'TCLO DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('12444', '3', 'OSI 687_Kristiansund_Inspection WMF18kV', 'P22005-05-03', '1', '151', 'TCLO ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('12460', '3', 'OSI 684_Asgard_Egersund_Elbow Box', 'P22005-05-04', '1', '151', 'LANC DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('12476', '3', 'OSI xx', 'P22005-05-05', '1', '151', 'CRÉÉ IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('12492', '3', 'OSI xx', 'P22005-05-06', '1', '151', 'CRÉÉ IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('12508', '3', 'OSI xx', 'P22005-05-07', '1', '151', 'CRÉÉ IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('12524', '3', 'OSI xx', 'P22005-05-08', '1', '151', 'CRÉÉ IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('12542', '3', 'COR AKSO - Subsea PBS', 'P22005-03-S1', '1', '151', 'LANC ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('12558', '3', 'COR MAN-ES - Test Jumper Repair', 'P22005-03-S2', '1', '151', 'LANC ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('12584', '3', 'SAV 3', 'P22005-03-S3', '1', '151', 'CRÉÉ IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('12604', '3', 'PHASE 1 - CONCEPT OF PROOF', 'P22006-01-FS', '1', '152', 'LANC DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('12745', '3', 'COMPONENT TESTING', 'P22006-01-CT', '1', '152', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('12761', '3', 'DESIGN & ENGINNERING 1', 'P22006-01-D1', '1', '152', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('12777', '3', 'DESIGN & ENGINNERING 2', 'P22006-01-D2', '1', '152', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('12793', '3', 'PROTOTYPE 1', 'P22006-01-P1', '1', '152', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('12809', '3', 'PROTOTYPE 2', 'P22006-01-P2', '1', '152', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('12825', '3', 'PROTOTYPE 3', 'P22006-01-P3', '1', '152', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('12841', '3', 'QUALIFICATION 1', 'P22006-01-Q1', '1', '152', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('12857', '3', 'QUALIFICATION 2', 'P22006-01-Q2', '1', '152', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('12873', '3', 'QUALIFICATION 3', 'P22006-01-Q3', '1', '152', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('12889', '3', 'FINAL DOCUMENTATION', 'P22006-01-FD', '1', '152', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('12910', '3', 'TRL2 HV Star End', 'P22007-01-FS', '1', '154', 'TCLO IMBL ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('12940', '3', 'nan', 'P22007-01-CT', '1', '154', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('12956', '3', 'nan', 'P22007-01-D1', '1', '154', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('12972', '3', 'nan', 'P22007-01-D2', '1', '154', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('12988', '3', 'nan', 'P22007-01-P1', '1', '154', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('13004', '3', 'nan', 'P22007-01-P2', '1', '154', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('13020', '3', 'nan', 'P22007-01-P3', '1', '154', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('13036', '3', 'nan', 'P22007-01-Q1', '1', '154', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('13052', '3', 'nan', 'P22007-01-Q2', '1', '154', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('13068', '3', 'nan', 'P22007-01-Q3', '1', '154', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('13084', '3', 'nan', 'P22007-01-FD', '1', '154', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('13105', '3', 'DESIGN', 'P22008-01-FS', '1', '155', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('13121', '3', 'COMPONENT TESTING', 'P22008-01-CT', '1', '155', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('13137', '3', 'DESIGN & ENGINNERING 1', 'P22008-01-D1', '1', '155', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('13153', '3', 'DESIGN & ENGINNERING 2', 'P22008-01-D2', '1', '155', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('13169', '3', 'PROTOTYPE Phase', 'P22008-01-P1', '1', '155', 'LANC DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('13888', '3', 'PROTOTYPE 2', 'P22008-01-P2', '1', '155', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('13904', '3', 'PROTOTYPE 3', 'P22008-01-P3', '1', '155', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('13920', '3', 'Phase QUALIFICATION 66kV', 'P22008-01-Q1', '1', '155', 'LANC DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('14007', '3', 'QUALIFICATION 2', 'P22008-01-Q2', '1', '155', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('14023', '3', 'QUALIFICATION 3', 'P22008-01-Q3', '1', '155', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('14039', '3', 'FINAL DOCUMENTATION', 'P22008-01-FD', '1', '155', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('14060', '3', 'nan', 'P22009-01-FS', '1', '156', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('14076', '3', 'nan', 'P22009-01-CT', '1', '156', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('14092', '3', 'nan', 'P22009-01-D1', '1', '156', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('14108', '3', 'nan', 'P22009-01-D2', '1', '156', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('14124', '3', 'PRODUCTION VESA', 'P22009-01-P1', '1', '156', 'LANC DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('14146', '3', 'nan', 'P22009-01-P2', '1', '156', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('14162', '3', 'nan', 'P22009-01-P3', '1', '156', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('14178', '3', 'nan', 'P22009-01-Q1', '1', '156', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('14194', '3', 'nan', 'P22009-01-Q2', '1', '156', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('14210', '3', 'nan', 'P22009-01-Q3', '1', '156', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('14226', '3', 'nan', 'P22009-01-FD', '1', '156', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('14247', '3', 'SCOPE A - Design Jumper Assembly', 'P22010-01-FS', '1', '157', 'TCLO IMBL ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('14263', '3', 'SCOPE B - Design Rear Terminat° &ShD CaP', 'P22010-01-CT', '1', '157', 'TCLO IMBL ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('14283', '3', 'PO71200045 Additional testing Prototype', 'P22010-01-D1', '1', '157', 'LANC ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('14299', '3', 'PO71200045 Additional testing Qualif', 'P22010-01-D2', '1', '157', 'LANC ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('14315', '3', 'SCOPE A - Prototypes/ Jumper', 'P22010-01-P1', '1', '157', 'TCLO IMBL ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('14382', '3', 'SCOPE B - Proto/Qualif embase+ shtd Cap', 'P22010-01-P2', '1', '157', 'TCLO IMBL ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('14495', '3', 'SCOPE A - 2nd barrier Design & Qualif', 'P22010-01-P3', '1', '157', 'TCLO IMBL ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('14517', '3', 'Retention Device - Design', 'P22010-01-Q1', '1', '157', 'TCLO IMBL ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('14533', '3', 'Retention Device - Prototype', 'P22010-01-Q2', '1', '157', 'TCLO IMBL ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('14562', '3', 'Retention Device - Qualification', 'P22010-01-Q3', '1', '157', 'LANC ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('14578', '3', 'nan', 'P22010-01-FD', '1', '157', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('14599', '3', 'FEASIBILITY STUDY_CAMERA 1 TETE', 'P22011-01-FS', '1', '158', 'LANC DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('14655', '3', 'FEASIBILITY STUDY_CAMERA MULTI TETES', 'P22011-01-CT', '1', '158', 'LANC DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('14684', '3', 'DEMONSTRATEUR', 'P22011-01-D1', '1', '158', 'LANC DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('14725', '3', 'HDCAM - WHD development', 'P22011-01-D2', '1', '158', 'LANC DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('14817', '3', 'LIVRAISON 3 CAMERA HDCAM avec WHD', 'P22011-01-P1', '1', '158', 'LANC ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('14838', '3', 'nan', 'P22011-01-P2', '1', '158', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('14854', '3', 'nan', 'P22011-01-P3', '1', '158', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('14870', '3', 'nan', 'P22011-01-Q1', '1', '158', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('14886', '3', 'nan', 'P22011-01-Q2', '1', '158', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('14902', '3', 'nan', 'P22011-01-Q3', '1', '158', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('14918', '3', 'nan', 'P22011-01-FD', '1', '158', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('14939', '3', 'nan', 'P22012-01-SP', '1', '159', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('14955', '3', 'nan', 'P22012-01-PE', '1', '159', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('14971', '3', 'SERIE Spare parts', 'P22012-01-PA', '1', '159', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('14994', '3', 'nan', 'P22012-02-FS', '1', '159', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('15010', '3', 'nan', 'P22012-02-CT', '1', '159', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('15026', '3', 'Fast Track PM Suport Plug', 'P22012-02-D1', '1', '159', 'TCLO IMBL ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('15042', '3', 'NRE Work for QPS-US certification', 'P22012-02-D2', '1', '159', 'TCLO IMBL ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('15058', '3', 'PM for rework of the receptacles', 'P22012-02-P1', '1', '159', 'TCLO IMBL ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('15074', '3', 'ITEM 50 - Extra-coûts certification QPS', 'P22012-02-P2', '1', '159', 'TCLO IMBL ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('15090', '3', 'nan', 'P22012-02-P3', '1', '159', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('15106', '3', 'nan', 'P22012-02-Q1', '1', '159', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('15122', '3', 'nan', 'P22012-02-Q2', '1', '159', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('15138', '3', 'nan', 'P22012-02-Q3', '1', '159', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('15154', '3', 'nan', 'P22012-02-FD', '1', '159', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('15174', '3', 'Concept Study', 'P23001-01-FS', '1', '160', 'TCLO IMBL ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('15194', '3', 'nan', 'P23001-01-CT', '1', '160', 'LANC DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('15224', '3', 'DESIGN & ENGINNERING 1', 'P23001-01-D1', '1', '160', 'TCLO IMBL ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('15435', '3', 'ITEM 1 - SUPPLY OF DUT for 2nd ageing', 'P23001-01-D2', '1', '160', 'TCLO BLOQ ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('15473', '3', 'NOVAK- Engineering Ex e JB PO 8070611', 'P23001-01-P1', '1', '160', 'TCLO IMBL ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('15501', '3', 'PO P/T test embases', 'P23001-01-P2', '1', '160', 'TCLO IMBL DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('15537', '3', 'ITEM 1 - PM SUPPORT & TESTING', 'P23001-01-P3', '1', '160', 'TCLO IMBL ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('15570', '3', 'ITEM 1 - SPARES 9316 71H 61-21/37-2 x6', 'P23001-01-Q1', '1', '160', 'TCLO IMBL ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('15596', '3', 'HEAT RISE TEST - EQN', 'P23001-01-Q2', '1', '160', 'TCLO IMBL ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('15628', '3', 'EQN - 3x Glass to Metal - NOVAK early', 'P23001-01-Q3', '1', '160', 'LANC IMBL ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('15650', '3', 'OSI/TRAINING', 'P23001-01-FD', '1', '160', 'TCLO IMBL ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('15667', '3', 'SERIE Project Execution', 'P23001-02-2', '1', '160', 'TCLO IMBL ACCE REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('15689', '3', 'nan', 'P23001-02-1', '1', '160', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('15705', '3', 'nan', 'P23001-02-3', '1', '160', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('15723', '3', 'He Leak test for x106 NOVAK HVP''s', 'P23001-03-FS', '1', '160', 'LANC IMBL ACCE', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('15739', '3', 'nan', 'P23001-03-CT', '1', '160', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('15755', '3', 'Test Procedure - PO8099427 pos 10', 'P23001-03-D1', '1', '160', 'TCLO IMBL ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('15771', '3', 'nan', 'P23001-03-D2', '1', '160', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('15787', '3', 'Test Preparation - PO8099427 pos 20', 'P23001-03-P1', '1', '160', 'TCLO IMBL ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('15803', '3', 'nan', 'P23001-03-P2', '1', '160', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('15819', '3', 'nan', 'P23001-03-P3', '1', '160', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('15835', '3', 'TEST - PO8099427 pos 30', 'P23001-03-Q1', '1', '160', 'TCLO IMBL ACCE REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('15851', '3', 'nan', 'P23001-03-Q2', '1', '160', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('15867', '3', 'nan', 'P23001-03-Q3', '1', '160', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('15883', '3', 'nan', 'P23001-03-FD', '1', '160', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('15901', '3', 'OSI 690 - 1/2', 'P23001-04-01', '1', '160', 'LANC DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('15917', '3', 'OSI 690 - 2/2', 'P23001-04-02', '1', '160', 'LANC DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('15933', '3', 'OSI 3', 'P23001-04-03', '1', '160', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('15949', '3', 'OSI 4', 'P23001-04-04', '1', '160', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('15969', '3', 'POLE INDUS VIE SERIE', 'P23002-01-FS', '1', '161', 'LANC', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('15985', '3', 'nan', 'P23002-01-CT', '1', '161', 'CRÉÉ BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('16001', '3', 'nan', 'P23002-01-D1', '1', '161', 'CRÉÉ BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('16017', '3', 'nan', 'P23002-01-D2', '1', '161', 'CRÉÉ BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('16033', '3', 'nan', 'P23002-01-P1', '1', '161', 'CRÉÉ BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('16049', '3', 'nan', 'P23002-01-P2', '1', '161', 'CRÉÉ BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('16065', '3', 'nan', 'P23002-01-P3', '1', '161', 'CRÉÉ BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('16081', '3', 'nan', 'P23002-01-Q1', '1', '161', 'CRÉÉ BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('16097', '3', 'nan', 'P23002-01-Q2', '1', '161', 'CRÉÉ BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('16113', '3', 'nan', 'P23002-01-Q3', '1', '161', 'CRÉÉ BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('16129', '3', 'nan', 'P23002-01-FD', '1', '161', 'CRÉÉ BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('16150', '3', 'FEASIBILITY STUDY', 'P23003-01-FS', '1', '162', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('16166', '3', 'COMPONENT TESTING', 'P23003-01-CT', '1', '162', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('16182', '3', 'DESIGN & ENGINEERING LP Splice_Star End', 'P23003-01-D1', '1', '162', 'TCLO BLOQ ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('16209', '3', 'DESIGN & ENGINEERING LP Splice_Star End', 'P23003-01-D2', '1', '162', 'TCLO BLOQ ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('16269', '3', 'PROTOTYPE 1', 'P23003-01-P1', '1', '162', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('16285', '3', 'PROTOTYPE 2', 'P23003-01-P2', '1', '162', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('16301', '3', 'PROTOTYPE 3', 'P23003-01-P3', '1', '162', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('16317', '3', 'QUALIFICATION 1', 'P23003-01-Q1', '1', '162', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('16333', '3', 'QUALIFICATION 2', 'P23003-01-Q2', '1', '162', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('16349', '3', 'QUALIFICATION 3', 'P23003-01-Q3', '1', '162', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('16365', '3', 'FINAL DOCUMENTATION', 'P23003-01-FD', '1', '162', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('16386', '3', 'FEASIBILITY STUDY', 'P23004-01-FS', '1', '163', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('16402', '3', 'COMPONENT TESTING', 'P23004-01-CT', '1', '163', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('16418', '3', 'DESIGN & ENGINNERING 1', 'P23004-01-D1', '1', '163', 'LANC BLOQ ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('16441', '3', 'DESIGN & ENGINNERING 2', 'P23004-01-D2', '1', '163', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('16457', '3', 'PROTOTYPE', 'P23004-01-P1', '1', '163', 'TCLO IMBL ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('16579', '3', 'PROTOTYPE 2', 'P23004-01-P2', '1', '163', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('16595', '3', 'PROTOTYPE 3', 'P23004-01-P3', '1', '163', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('16611', '3', 'QUALIFICATION ODB', 'P23004-01-Q1', '1', '163', 'LANC ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('16758', '3', 'QUALIFICATION 2', 'P23004-01-Q2', '1', '163', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('16774', '3', 'QUALIFICATION 3', 'P23004-01-Q3', '1', '163', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('16790', '3', 'FINAL DOCUMENTATION', 'P23004-01-FD', '1', '163', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('16811', '3', 'CONCEPT STUDY (FEED)', 'P23005-01-FS', '1', '164', 'TCLO IMBL ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('16867', '3', 'LIVRAISON SPLICES ET PLATES LSR CREAM', 'P23005-01-CT', '1', '164', 'TCLO IMBL ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('16899', '3', 'DESIGN & ENGINEERING1', 'P23005-01-D1', '1', '164', 'LANC IMBL ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('16915', '3', 'CONCEPT STUDY (FEED) MOCK-UP', 'P23005-01-D2', '1', '164', 'LANC ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('16994', '3', 'CONCEPT STUDY (FEED) PHASE 2', 'P23005-01-P1', '1', '164', 'LANC IMBL ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('17015', '3', 'PROTOTYPE 2 /NA', 'P23005-01-P2', '1', '164', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('17031', '3', 'PROTOTYPE 3 /NA', 'P23005-01-P3', '1', '164', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('17047', '3', 'QUALIFICATION 1 /NA', 'P23005-01-Q1', '1', '164', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('17063', '3', 'QUALIFICATION 2 /NA', 'P23005-01-Q2', '1', '164', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('17079', '3', 'QUALIFICATION 3 /NA', 'P23005-01-Q3', '1', '164', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('17095', '3', 'FINAL DOCUMENTATION /NA', 'P23005-01-FD', '1', '164', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('17116', '3', 'nan', 'P23006-01-FS', '1', '165', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('17132', '3', 'nan', 'P23006-01-CT', '1', '165', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('17148', '3', 'DESIGN & ENGINNERING 1', 'P23006-01-D1', '1', '165', 'TCLO IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('17164', '3', 'nan', 'P23006-01-D2', '1', '165', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('17180', '3', 'nan', 'P23006-01-P1', '1', '165', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('17196', '3', 'nan', 'P23006-01-P2', '1', '165', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('17212', '3', 'nan', 'P23006-01-P3', '1', '165', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('17228', '3', 'nan', 'P23006-01-Q1', '1', '165', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('17244', '3', 'nan', 'P23006-01-Q2', '1', '165', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('17260', '3', 'nan', 'P23006-01-Q3', '1', '165', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('17276', '3', 'nan', 'P23006-01-FD', '1', '165', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('17297', '3', 'nan', 'P23007-01-FS', '1', '166', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('17313', '3', 'nan', 'P23007-01-CT', '1', '166', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('17329', '3', 'DESIGN & ENGINNERING 1', 'P23007-01-D1', '1', '166', 'LANC', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('17345', '3', 'nan', 'P23007-01-D2', '1', '166', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('17361', '3', 'nan', 'P23007-01-P1', '1', '166', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('17377', '3', 'nan', 'P23007-01-P2', '1', '166', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('17393', '3', 'nan', 'P23007-01-P3', '1', '166', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('17409', '3', 'nan', 'P23007-01-Q1', '1', '166', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('17425', '3', 'nan', 'P23007-01-Q2', '1', '166', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('17441', '3', 'nan', 'P23007-01-Q3', '1', '166', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('17457', '3', 'nan', 'P23007-01-FD', '1', '166', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('17478', '3', 'nan', 'P23008-01-FS', '1', '167', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('17494', '3', 'nan', 'P23008-01-CT', '1', '167', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('17510', '3', 'nan', 'P23008-01-D1', '1', '167', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('17526', '3', 'nan', 'P23008-01-D2', '1', '167', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('17542', '3', 'PROTOTYPE 1', 'P23008-01-P1', '1', '167', 'LANC', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('17558', '3', 'nan', 'P23008-01-P2', '1', '167', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('17574', '3', 'nan', 'P23008-01-P3', '1', '167', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('17590', '3', 'QUALIFICATION 1', 'P23008-01-Q1', '1', '167', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('17606', '3', 'nan', 'P23008-01-Q2', '1', '167', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('17622', '3', 'nan', 'P23008-01-Q3', '1', '167', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('17638', '3', 'nan', 'P23008-01-FD', '1', '167', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('17659', '3', 'FEASIBILITY STUDY', 'P23009-01-FS', '1', '168', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('17675', '3', 'nan', 'P23009-01-CT', '1', '168', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('17691', '3', 'DESIGN & ENGINNERING 1', 'P23009-01-D1', '1', '168', 'LANC', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('17707', '3', 'nan', 'P23009-01-D2', '1', '168', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('17723', '3', 'PROTOTYPE 1', 'P23009-01-P1', '1', '168', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('17739', '3', 'nan', 'P23009-01-P2', '1', '168', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('17755', '3', 'nan', 'P23009-01-P3', '1', '168', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('17771', '3', 'QUALIFICATION 1', 'P23009-01-Q1', '1', '168', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('17787', '3', 'nan', 'P23009-01-Q2', '1', '168', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('17803', '3', 'nan', 'P23009-01-Q3', '1', '168', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('17819', '3', 'nan', 'P23009-01-FD', '1', '168', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('17840', '3', 'nan', 'P23012-01-FS', '1', '171', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('17856', '3', 'nan', 'P23012-01-CT', '1', '171', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('17872', '3', 'nan', 'P23012-01-D1', '1', '171', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('17888', '3', 'nan', 'P23012-01-D2', '1', '171', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('17904', '3', 'PROTOTYPE 1', 'P23012-01-P1', '1', '171', 'LANC', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('17939', '3', 'nan', 'P23012-01-P2', '1', '171', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('17955', '3', 'nan', 'P23012-01-P3', '1', '171', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('17971', '3', 'QUALIFICATION 1', 'P23012-01-Q1', '1', '171', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('17987', '3', 'nan', 'P23012-01-Q2', '1', '171', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('18003', '3', 'nan', 'P23012-01-Q3', '1', '171', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('18019', '3', 'nan', 'P23012-01-FD', '1', '171', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('18040', '3', 'SERIE PO', 'P23013-01-SP', '1', '172', 'LANC ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('18139', '3', 'SERIE Project Execution', 'P23013-01-PE', '1', '172', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('18155', '3', 'SERIE Spare parts', 'P23013-01-PA', '1', '172', 'TCLO IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('18174', '3', 'Support projet JSM - PM1', 'P23013-02-FS', '1', '172', 'TCLO IMBL ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('18196', '3', 'Support projet JSM - PM3', 'P23013-02-CT', '1', '172', 'TCLO IMBL ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('18212', '3', 'Support projet JSM - PM4', 'P23013-02-D1', '1', '172', 'TCLO IMBL ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('18228', '3', 'Support projet JSM - PM5', 'P23013-02-D2', '1', '172', 'TCLO IMBL ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('18244', '3', 'Support projet JSM - PM2', 'P23013-02-P1', '1', '172', 'TCLO IMBL ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('18260', '3', 'JSM - Qualif boot 330 bar', 'P23013-02-P2', '1', '172', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('18286', '3', 'OSI 704 (Jack South)', 'P23013-02-P3', '1', '172', 'LANC DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('18302', '3', 'nan', 'P23013-02-Q1', '1', '172', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('18318', '3', 'nan', 'P23013-02-Q2', '1', '172', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('18334', '3', 'nan', 'P23013-02-Q3', '1', '172', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('18350', '3', 'nan', 'P23013-02-FD', '1', '172', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('8284', '3', 'MW - FEASIBILITY STUDY', 'P21003-01-FS', '1', '173', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('8300', '3', 'COMPONENT TESTING', 'P21003-01-CT', '1', '173', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('8316', '3', 'DESIGN & ENGINNERING 1', 'P21003-01-D1', '1', '173', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('8332', '3', 'DESIGN & ENGINNERING 2', 'P21003-01-D2', '1', '173', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('8348', '3', 'PROTOTYPE 1', 'P21003-01-P1', '1', '173', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('8364', '3', 'PROTOTYPE 2', 'P21003-01-P2', '1', '173', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('8380', '3', 'PROTOTYPE 3', 'P21003-01-P3', '1', '173', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('8396', '3', 'QUALIFICATION 1', 'P21003-01-Q1', '1', '173', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('8412', '3', 'QUALIFICATION 2', 'P21003-01-Q2', '1', '173', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('8428', '3', 'QUALIFICATION 3', 'P21003-01-Q3', '1', '173', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('8444', '3', 'FINAL DOCUMENTATION', 'P21003-01-FD', '1', '173', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('8463', '3', 'KW - FEASIBILITY STUDY', 'P21003-02-FS', '1', '173', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('8579', '3', 'COMPONENT TESTING', 'P21003-02-CT', '1', '173', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('8595', '3', 'DESIGN & ENGINNERING 1', 'P21003-02-D1', '1', '173', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('8611', '3', 'DESIGN & ENGINNERING 2', 'P21003-02-D2', '1', '173', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('8627', '3', 'PROTOTYPE 1', 'P21003-02-P1', '1', '173', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('8651', '3', 'PROTOTYPE 2', 'P21003-02-P2', '1', '173', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('8667', '3', 'PROTOTYPE 3', 'P21003-02-P3', '1', '173', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('8683', '3', 'QUALIFICATION 1', 'P21003-02-Q1', '1', '173', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('8699', '3', 'QUALIFICATION 2', 'P21003-02-Q2', '1', '173', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('8715', '3', 'QUALIFICATION 3', 'P21003-02-Q3', '1', '173', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('8731', '3', 'FINAL DOCUMENTATION', 'P21003-02-FD', '1', '173', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('8749', '3', 'FEASIBILITY STUDY', 'P21003-03-FS', '1', '173', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('8765', '3', 'COMPONENT TESTING', 'P21003-03-CT', '1', '173', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('8781', '3', 'DESIGN - HHV KW', 'P21003-03-D1', '1', '173', 'LANC', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('8797', '3', 'DESIGN & ENGINNERING 2', 'P21003-03-D2', '1', '173', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('8813', '3', 'PROTO - HHV KW', 'P21003-03-P1', '1', '173', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('8885', '3', 'PROTOTYPE 2', 'P21003-03-P2', '1', '173', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('8901', '3', 'PROTOTYPE 3', 'P21003-03-P3', '1', '173', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('8917', '3', 'VALIDATION TEST - HHV KW', 'P21003-03-Q1', '1', '173', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('8933', '3', 'QUALIFICATION 2', 'P21003-03-Q2', '1', '173', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('8949', '3', 'QUALIFICATION 3', 'P21003-03-Q3', '1', '173', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('8965', '3', 'FINAL DOCUMENTATION', 'P21003-03-FD', '1', '173', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('18551', '3', 'FEASIBILITY STUDY', 'P23015-01-FS', '1', '174', 'TCLO IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('18567', '3', 'nan', 'P23015-01-CT', '1', '174', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('18583', '3', 'DESIGN & ENGINNERING 1', 'P23015-01-D1', '1', '174', 'TCLO IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('18632', '3', 'nan', 'P23015-01-D2', '1', '174', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('18648', '3', 'PROTOTYPE 1', 'P23015-01-P1', '1', '174', 'TCLO DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('18690', '3', 'nan', 'P23015-01-P2', '1', '174', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('18706', '3', 'nan', 'P23015-01-P3', '1', '174', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('18722', '3', 'QUALIFICATION 1', 'P23015-01-Q1', '1', '174', 'LANC DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('18764', '3', 'nan', 'P23015-01-Q2', '1', '174', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('18780', '3', 'nan', 'P23015-01-Q3', '1', '174', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('18796', '3', 'nan', 'P23015-01-FD', '1', '174', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('18817', '3', 'nan', 'P23016-01-FS', '1', '175', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('18833', '3', 'nan', 'P23016-01-CT', '1', '175', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('18849', '3', 'DESIGN & ENGINNERING 1', 'P23016-01-D1', '1', '175', 'LANC', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('18912', '3', 'nan', 'P23016-01-D2', '1', '175', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('18928', '3', 'PROTOTYPE 1', 'P23016-01-P1', '1', '175', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('18954', '3', 'nan', 'P23016-01-P2', '1', '175', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('18970', '3', 'nan', 'P23016-01-P3', '1', '175', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('18986', '3', 'QUALIFICATION 1', 'P23016-01-Q1', '1', '175', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('19002', '3', 'nan', 'P23016-01-Q2', '1', '175', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('19018', '3', 'nan', 'P23016-01-Q3', '1', '175', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('19034', '3', 'nan', 'P23016-01-FD', '1', '175', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('19055', '3', 'ENVERRE', 'P23017-01-FS', '1', '176', 'LANC', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('19077', '3', 'ISOLANT ARRIERE EMBASE', 'P23017-01-CT', '1', '176', 'LANC', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('19097', '3', 'CONTACT FICHE', 'P23017-01-D1', '1', '176', 'LANC', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('19118', '3', 'GROMET FICHE', 'P23017-01-D2', '1', '176', 'LANC', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('19134', '3', 'TAPIS INTERFACIAUX', 'P23017-01-P1', '1', '176', 'LANC', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('19189', '3', 'PROTOTYPES SOP 641-003 FICHES', 'P23017-01-P2', '1', '176', 'LANC DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('19299', '3', 'INVEST MOULES ET OUTILLAGES', 'P23017-01-P3', '1', '176', 'LANC', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('19434', '3', 'QUALIF FICHES SOP 641-003', 'P23017-01-Q1', '1', '176', 'LANC', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('19458', '3', 'nan', 'P23017-01-Q2', '1', '176', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('19474', '3', 'nan', 'P23017-01-Q3', '1', '176', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('19490', '3', 'nan', 'P23017-01-FD', '1', '176', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('19511', '3', 'FEASIBILITY STUDY', 'P23018-01-FS', '1', '177', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('19527', '3', 'COMPONENT TESTING', 'P23018-01-CT', '1', '177', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('19543', '3', 'DESIGN & ENGINNERING 1', 'P23018-01-D1', '1', '177', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('19559', '3', 'DESIGN & ENGINNERING 2', 'P23018-01-D2', '1', '177', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('19575', '3', 'PROTOTYPE 1', 'P23018-01-P1', '1', '177', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('19591', '3', 'PROTOTYPE 2', 'P23018-01-P2', '1', '177', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('19607', '3', 'PROTOTYPE 3', 'P23018-01-P3', '1', '177', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('19623', '3', 'Support Engineering MSW-276-8028-14', 'P23018-01-Q1', '1', '177', 'LANC ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('19696', '3', 'QUALIFICATION 2', 'P23018-01-Q2', '1', '177', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('19712', '3', 'QUALIFICATION 3', 'P23018-01-Q3', '1', '177', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('19728', '3', 'FINAL DOCUMENTATION', 'P23018-01-FD', '1', '177', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('19749', '3', 'nan', 'P24001-01-FS', '1', '179', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('19765', '3', 'nan', 'P24001-01-CT', '1', '179', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('19781', '3', 'DESIGN & ENGINNERING 1', 'P24001-01-D1', '1', '179', 'LANC DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('19806', '3', 'nan', 'P24001-01-D2', '1', '179', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('19822', '3', 'PROTOTYPE 1', 'P24001-01-P1', '1', '179', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('19838', '3', 'nan', 'P24001-01-P2', '1', '179', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('19854', '3', 'nan', 'P24001-01-P3', '1', '179', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('19870', '3', 'QUALIFICATION 1', 'P24001-01-Q1', '1', '179', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('19886', '3', 'nan', 'P24001-01-Q2', '1', '179', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('19902', '3', 'nan', 'P24001-01-Q3', '1', '179', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('19918', '3', 'nan', 'P24001-01-FD', '1', '179', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('19939', '3', 'FEASIBILITY STUDY', 'P24002-01-FS', '1', '180', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('19955', '3', 'COMPONENT TESTING', 'P24002-01-CT', '1', '180', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('19971', '3', 'DESIGN & ENGINNERING 1', 'P24002-01-D1', '1', '180', 'LANC', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('19987', '3', 'DESIGN & ENGINNERING 2', 'P24002-01-D2', '1', '180', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('20003', '3', 'PROTOTYPE 1', 'P24002-01-P1', '1', '180', 'LANC DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('20050', '3', 'PROTOTYPE 2', 'P24002-01-P2', '1', '180', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('20066', '3', 'PROTOTYPE 3', 'P24002-01-P3', '1', '180', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('20082', '3', 'QUALIFICATION 1', 'P24002-01-Q1', '1', '180', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('20098', '3', 'QUALIFICATION 2', 'P24002-01-Q2', '1', '180', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('20114', '3', 'QUALIFICATION 3', 'P24002-01-Q3', '1', '180', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('20130', '3', 'FINAL DOCUMENTATION', 'P24002-01-FD', '1', '180', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('20151', '3', 'FEASIBILITY STUDY', 'P24003-01-FS', '1', '181', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('20167', '3', 'COMPONENT TESTING', 'P24003-01-CT', '1', '181', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('20183', '3', 'DESIGN & ENGINNERING 1', 'P24003-01-D1', '1', '181', 'LANC', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('20199', '3', 'DESIGN & ENGINNERING 2', 'P24003-01-D2', '1', '181', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('20215', '3', 'PROTOTYPE 1', 'P24003-01-P1', '1', '181', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('20231', '3', 'PROTOTYPE 2', 'P24003-01-P2', '1', '181', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('20247', '3', 'PROTOTYPE 3', 'P24003-01-P3', '1', '181', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('20263', '3', 'QUALIFICATION 1', 'P24003-01-Q1', '1', '181', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('20279', '3', 'QUALIFICATION 2', 'P24003-01-Q2', '1', '181', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('20295', '3', 'QUALIFICATION 3', 'P24003-01-Q3', '1', '181', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('20311', '3', 'FINAL DOCUMENTATION', 'P24003-01-FD', '1', '181', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('20332', '3', 'FEASIBILITY STUDY', 'P24004-01-FS', '1', '182', 'TCLO IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('20348', '3', 'COMPONENT TESTING', 'P24004-01-CT', '1', '182', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('20364', '3', 'DESIGN & ENGINNERING 1', 'P24004-01-D1', '1', '182', 'TCLO IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('20380', '3', 'nan', 'P24004-01-D2', '1', '182', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('20396', '3', 'PROTOTYPE 1', 'P24004-01-P1', '1', '182', 'TCLO IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('20412', '3', 'nan', 'P24004-01-P2', '1', '182', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('20428', '3', 'nan', 'P24004-01-P3', '1', '182', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('20444', '3', 'QUALIFICATION 1', 'P24004-01-Q1', '1', '182', 'TCLO IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('20460', '3', 'nan', 'P24004-01-Q2', '1', '182', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('20476', '3', 'nan', 'P24004-01-Q3', '1', '182', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('20492', '3', 'FINAL DOCUMENTATION', 'P24004-01-FD', '1', '182', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('20513', '3', 'Rework procedure', 'P24005-01-S1', '1', '183', 'LANC ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('20529', '3', 'Procurement repair kit', 'P24005-01-S2', '1', '183', 'LANC ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('20585', '3', 'Field Service', 'P24005-01-S3', '1', '183', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('20606', '3', 'nan', 'P24006-01-FS', '1', '184', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('20622', '3', 'ETUDE CONCEPT', 'P24006-01-CT', '1', '184', 'TCLO BLOQ ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('20638', '3', 'DESIGN & ENGINEERING', 'P24006-01-D1', '1', '184', 'LANC ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('20654', '3', 'nan', 'P24006-01-D2', '1', '184', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('20670', '3', 'SCOPE OF PROTOTYPE OPTICAL', 'P24006-01-P1', '1', '184', 'LANC ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('20724', '3', 'SCOPE OF PROTOTYPE ELECTRICAL', 'P24006-01-P2', '1', '184', 'LANC ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('20778', '3', 'SOD Client Elec + Optique', 'P24006-01-P3', '1', '184', 'LANC DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('20800', '3', 'QUALIFICATION', 'P24006-01-Q1', '1', '184', 'LANC ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('20816', '3', 'nan', 'P24006-01-Q2', '1', '184', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('20832', '3', 'nan', 'P24006-01-Q3', '1', '184', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('20848', '3', 'nan', 'P24006-01-FD', '1', '184', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('20869', '3', 'FEED STUDY', 'P24007-01-FS', '1', '185', 'LANC ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('20885', '3', 'COMPONENT TESTING', 'P24007-01-CT', '1', '185', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('20901', '3', 'DESIGN & ENGINNERING 1', 'P24007-01-D1', '1', '185', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('20917', '3', 'DESIGN & ENGINNERING 2', 'P24007-01-D2', '1', '185', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('20933', '3', 'PROTOTYPE 1', 'P24007-01-P1', '1', '185', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('20949', '3', 'PROTOTYPE 2', 'P24007-01-P2', '1', '185', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('20965', '3', 'PROTOTYPE 3', 'P24007-01-P3', '1', '185', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('20981', '3', 'QUALIFICATION 1', 'P24007-01-Q1', '1', '185', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('20997', '3', 'QUALIFICATION 2', 'P24007-01-Q2', '1', '185', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('21013', '3', 'QUALIFICATION 3', 'P24007-01-Q3', '1', '185', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('21029', '3', 'FINAL DOCUMENTATION', 'P24007-01-FD', '1', '185', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('21050', '3', 'nan', 'P24008-01-FS', '1', '186', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('21066', '3', 'nan', 'P24008-01-CT', '1', '186', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('21082', '3', 'DESIGN & ENGINNERING 1', 'P24008-01-D1', '1', '186', 'LANC ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('21148', '3', 'nan', 'P24008-01-D2', '1', '186', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('21164', '3', 'PROTOTYPE 1', 'P24008-01-P1', '1', '186', 'LANC ACCE DELN REGU', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('21207', '3', 'SOD Client Prototype', 'P24008-01-P2', '1', '186', 'LANC ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('21239', '3', 'nan', 'P24008-01-P3', '1', '186', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('21255', '3', 'QUALIFICATION 1', 'P24008-01-Q1', '1', '186', 'LANC ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('21271', '3', 'nan', 'P24008-01-Q2', '1', '186', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('21287', '3', 'nan', 'P24008-01-Q3', '1', '186', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('21303', '3', 'nan', 'P24008-01-FD', '1', '186', 'LANC BLOQ DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('21324', '3', 'Etudes SSH-JIP - WP2, WP3, WP4 - Phase 1', 'P24009-01-FS', '1', '187', 'LANC ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('21340', '3', 'Etudes SSH-JIP - WP2, WP3, WP4 - Phase 2', 'P24009-01-CT', '1', '187', 'LANC ACCE DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('21356', '3', 'nan', 'P24009-01-D1', '1', '187', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('21372', '3', 'nan', 'P24009-01-D2', '1', '187', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('21388', '3', 'nan', 'P24009-01-P1', '1', '187', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('21404', '3', 'nan', 'P24009-01-P2', '1', '187', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('21420', '3', 'nan', 'P24009-01-P3', '1', '187', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('21436', '3', 'nan', 'P24009-01-Q1', '1', '187', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('21452', '3', 'nan', 'P24009-01-Q2', '1', '187', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('21468', '3', 'nan', 'P24009-01-Q3', '1', '187', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('21484', '3', 'nan', 'P24009-01-FD', '1', '187', 'LANC IMBL DELN', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('21505', '3', 'FEASIBILITY STUDY', 'P24010-01-FS', '1', '188', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('21521', '3', 'COMPONENT TESTING', 'P24010-01-CT', '1', '188', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('21537', '3', 'DESIGN & ENGINNERING 1', 'P24010-01-D1', '1', '188', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('21553', '3', 'nan', 'P24010-01-D2', '1', '188', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('21569', '3', 'PROTOTYPE 1', 'P24010-01-P1', '1', '188', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('21585', '3', 'nan', 'P24010-01-P2', '1', '188', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('21601', '3', 'nan', 'P24010-01-P3', '1', '188', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('21617', '3', 'QUALIFICATION 1', 'P24010-01-Q1', '1', '188', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('21633', '3', 'nan', 'P24010-01-Q2', '1', '188', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('21649', '3', 'nan', 'P24010-01-Q3', '1', '188', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('21665', '3', 'FINAL DOCUMENTATION', 'P24010-01-FD', '1', '188', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('21686', '3', 'FEASIBILITY STUDY', 'P25001-01-FS', '1', '189', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('21702', '3', 'nan', 'P25001-01-CT', '1', '189', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('21718', '3', 'DESIGN & ENGINNERING 1', 'P25001-01-D1', '1', '189', 'LANC', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('21734', '3', 'nan', 'P25001-01-D2', '1', '189', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('21750', '3', 'nan', 'P25001-01-P1', '1', '189', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('21766', '3', 'nan', 'P25001-01-P2', '1', '189', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('21782', '3', 'nan', 'P25001-01-P3', '1', '189', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('21798', '3', 'nan', 'P25001-01-Q1', '1', '189', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('21814', '3', 'nan', 'P25001-01-Q2', '1', '189', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('21830', '3', 'nan', 'P25001-01-Q3', '1', '189', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('21846', '3', 'nan', 'P25001-01-FD', '1', '189', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('21867', '3', 'FEASIBILITY STUDY', 'P25002-01-FS', '1', '190', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('21883', '3', 'nan', 'P25002-01-CT', '1', '190', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('21899', '3', 'DESIGN & ENGINNERING 1', 'P25002-01-D1', '1', '190', 'LANC', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('21915', '3', 'nan', 'P25002-01-D2', '1', '190', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('21931', '3', 'nan', 'P25002-01-P1', '1', '190', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('21947', '3', 'nan', 'P25002-01-P2', '1', '190', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('21963', '3', 'nan', 'P25002-01-P3', '1', '190', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('21979', '3', 'nan', 'P25002-01-Q1', '1', '190', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('21995', '3', 'nan', 'P25002-01-Q2', '1', '190', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('22011', '3', 'nan', 'P25002-01-Q3', '1', '190', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('22027', '3', 'nan', 'P25002-01-FD', '1', '190', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('22048', '3', 'FEASIBILITY STUDY', 'P25003-01-FS', '1', '191', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('22064', '3', 'nan', 'P25003-01-CT', '1', '191', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('22080', '3', 'DESIGN & ENGINEERING1', 'P25003-01-D1', '1', '191', 'LANC', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('22096', '3', 'nan', 'P25003-01-D2', '1', '191', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('22112', '3', 'nan', 'P25003-01-P1', '1', '191', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('22128', '3', 'nan', 'P25003-01-P2', '1', '191', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('22144', '3', 'nan', 'P25003-01-P3', '1', '191', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('22160', '3', 'nan', 'P25003-01-Q1', '1', '191', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('22176', '3', 'nan', 'P25003-01-Q2', '1', '191', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('22192', '3', 'nan', 'P25003-01-Q3', '1', '191', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('22208', '3', 'nan', 'P25003-01-FD', '1', '191', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('22229', '3', 'FEASIBILITY STUDY', 'P25004-01-FS', '1', '192', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('22245', '3', 'nan', 'P25004-01-CT', '1', '192', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('22261', '3', 'DESIGN & ENGINNERING 1', 'P25004-01-D1', '1', '192', 'LANC', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('22277', '3', 'nan', 'P25004-01-D2', '1', '192', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('22293', '3', 'nan', 'P25004-01-P1', '1', '192', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('22309', '3', 'nan', 'P25004-01-P2', '1', '192', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('22325', '3', 'nan', 'P25004-01-P3', '1', '192', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('22341', '3', 'nan', 'P25004-01-Q1', '1', '192', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('22357', '3', 'nan', 'P25004-01-Q2', '1', '192', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('22373', '3', 'nan', 'P25004-01-Q3', '1', '192', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('22389', '3', 'nan', 'P25004-01-FD', '1', '192', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('22410', '3', 'FEASIBILITY STUDY', 'P25005-01-FS', '1', '193', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('22426', '3', 'nan', 'P25005-01-CT', '1', '193', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('22442', '3', 'DESIGN & ENGINNERING 1', 'P25005-01-D1', '1', '193', 'LANC', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('22458', '3', 'nan', 'P25005-01-D2', '1', '193', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('22474', '3', 'nan', 'P25005-01-P1', '1', '193', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('22490', '3', 'nan', 'P25005-01-P2', '1', '193', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('22506', '3', 'nan', 'P25005-01-P3', '1', '193', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('22522', '3', 'nan', 'P25005-01-Q1', '1', '193', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('22538', '3', 'nan', 'P25005-01-Q2', '1', '193', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('22554', '3', 'nan', 'P25005-01-Q3', '1', '193', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('22570', '3', 'nan', 'P25005-01-FD', '1', '193', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('22591', '3', 'FEASIBILITY STUDY', 'P25006-01-FS', '1', '194', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('22607', '3', 'nan', 'P25006-01-CT', '1', '194', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('22623', '3', 'DESIGN & ENGINNERING 1', 'P25006-01-D1', '1', '194', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('22639', '3', 'nan', 'P25006-01-D2', '1', '194', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('22655', '3', 'nan', 'P25006-01-P1', '1', '194', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('22671', '3', 'nan', 'P25006-01-P2', '1', '194', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('22687', '3', 'nan', 'P25006-01-P3', '1', '194', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('22703', '3', 'nan', 'P25006-01-Q1', '1', '194', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('22719', '3', 'nan', 'P25006-01-Q2', '1', '194', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('22735', '3', 'nan', 'P25006-01-Q3', '1', '194', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('22751', '3', 'nan', 'P25006-01-FD', '1', '194', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('22772', '3', 'FEASIBILITY STUDY', 'P25007-01-FS', '1', '195', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('22788', '3', 'nan', 'P25007-01-CT', '1', '195', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('22804', '3', 'DESIGN & ENGINNERING 1', 'P25007-01-D1', '1', '195', 'LANC IMBL', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('22820', '3', 'nan', 'P25007-01-D2', '1', '195', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('22836', '3', 'nan', 'P25007-01-P1', '1', '195', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('22852', '3', 'nan', 'P25007-01-P2', '1', '195', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('22868', '3', 'nan', 'P25007-01-P3', '1', '195', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('22884', '3', 'nan', 'P25007-01-Q1', '1', '195', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('22900', '3', 'nan', 'P25007-01-Q2', '1', '195', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('22916', '3', 'nan', 'P25007-01-Q3', '1', '195', 'LANC BLOQ', 1, 'old phase test');
INSERT INTO phase (id, level, title, code, status, projet_id, status_txt, status_manuel, commentaire) VALUES ('22932', '3', 'nan', 'P25007-01-FD', '1', '195', 'LANC BLOQ', 1, 'old phase test');
